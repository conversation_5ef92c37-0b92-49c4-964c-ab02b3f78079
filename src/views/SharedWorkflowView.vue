<script setup lang="ts">
	import { ref, computed, onMounted, defineAsyncComponent } from "vue";
	import { useRoute, useRouter } from "vue-router";
	import { useI18n } from "vue-i18n";
	import { Error, Success } from "@/utils/notify";
	import {
		PublicSharesAPI,
		type PublicShareInfo,
		type SharedWorkflowInfo,
	} from "@/api/shares";
	import {
		ShareIcon,
		PlayIcon,
		DocumentDuplicateIcon,
		ExclamationTriangleIcon,
		ArrowLeftIcon,
	} from "@heroicons/vue/24/outline";
	import component_wait from "@/components/utils/component_wait.vue";
	import { useNodeOptions } from "@/composables/useNodeOptions";

	const { t } = useI18n();
	const route = useRoute();
	const router = useRouter();

	// 异步加载组件
	const Workflow = defineAsyncComponent(() => import("@/components/workflow.vue"));
	const RuntimeEngine = defineAsyncComponent(
		() => import("@/components/utils/runtime_engine.vue")
	);
	const Config = defineAsyncComponent(() => import("@/components/config.vue"));

	const shareUuid = route.params.shareUuid as string;
	const shareInfo = ref<PublicShareInfo | null>(null);
	const workflowInfo = ref<SharedWorkflowInfo | null>(null);
	const loading = ref(true);
	const error = ref<string | null>(null);
	const currentView = ref<"workflow" | "runtime">("workflow");

	// Workflow 相关状态
	const flowId = ref(`shared-workflow-${shareUuid}`);
	const workflowRef = ref();

	// Runtime 相关状态
	const runtimeData = ref<any>(null);
	const showRuntime = ref(false);

	// Config 相关状态管理
	const showConfig = ref(false);
	const configNodeID = ref("");
	const configNodeType = ref("");
	const configWorkflow = ref(flowId.value);

	const isExpired = computed(() => {
		if (!shareInfo.value?.expires_at) return false;
		return new Date(shareInfo.value.expires_at) < new Date();
	});

	const canRun = computed(() => {
		return shareInfo.value?.ui_config.allow_run && !isExpired.value;
	});

	const canCopy = computed(() => {
		return shareInfo.value?.ui_config.allow_copy && !isExpired.value;
	});

	const parsedWorkflowData = computed(() => {
		if (!workflowInfo.value?.data) return null;
		try {
			return typeof workflowInfo.value.data === "string"
				? JSON.parse(workflowInfo.value.data)
				: workflowInfo.value.data;
		} catch (e) {
			console.error("Failed to parse workflow data:", e);
			return null;
		}
	});

	const loadShareInfo = async () => {
		try {
			loading.value = true;
			error.value = null;

			const info = await PublicSharesAPI.getPublicShareInfo(shareUuid);
			shareInfo.value = info;

			// 加载 workflow 详细信息
			const workflowData = await PublicSharesAPI.getSharedWorkflowInfo(shareUuid);
			workflowInfo.value = workflowData;
		} catch (err) {
			console.error(err);
			error.value = err as string;
		} finally {
			loading.value = false;
		}
	};

	const handleRun = () => {
		if (!canRun.value) return;

		const flowObject = workflowRef.value?.run();
		if (!flowObject) return;

		runtimeData.value = flowObject;
		showRuntime.value = true;
		currentView.value = "runtime";
	};

	const handleCopyWorkflow = async () => {
		if (!canCopy.value) return;

		try {
			const copyData = {
				name: `Copy of ${shareInfo.value?.workflow_name}`,
				description: `Copied from shared workflow: ${shareInfo.value?.title}`,
			};

			const result = await PublicSharesAPI.copySharedWorkflow(shareUuid, copyData);
			Success(t("success"), t("shares.workflow_copied"));

			// Redirect to the copied workflow
			router.push(`/projects/${result.workflow.uuid}`);
		} catch (err) {
			Error(t("error"), err as string);
		}
	};

	const closeConfig = () => {
		showConfig.value = false;
		workflowRef.value?.focus();
	};

	const onNodeConfig = (id: string, type: string, subWorkflow?: any) => {
		configNodeID.value = id;
		configNodeType.value = type;
		configWorkflow.value = flowId.value;

		if (subWorkflow) {
			configWorkflow.value = subWorkflow;
		}

		showConfig.value = true;
	};

	// 在共享模式下禁用删除功能
	const deleteNode = (id: string) => {
		// 在共享模式下不允许删除节点
		console.warn("Node deletion is disabled in shared workflow view");
	};

	const { options: nodeOptions } = useNodeOptions();

	onMounted(() => {
		loadShareInfo();
	});
</script>

<template>
	<div class="w-full min-h-screen">
		<!-- Loading State -->
		<div
			class="w-full min-h-screen flex justify-center items-center flex-col"
			v-if="loading"
		>
			<span class="loading loading-ring loading-lg"></span>
			<span class="text-md text-center mt-3 text-gray-500">{{ t("loading") }}</span>
		</div>

		<!-- Error State -->
		<div v-else-if="error" class="w-full min-h-screen flex justify-center items-center">
			<div class="max-w-2xl mx-auto">
				<div class="alert alert-error">
					<ExclamationTriangleIcon class="w-6 h-6" />
					<div>
						<h3 class="font-bold">{{ t("shares.error_loading_share") }}</h3>
						<div class="text-sm">{{ error }}</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Main Content -->
		<div class="min-h-screen flex flex-col" v-else-if="shareInfo && workflowInfo">
			<!-- Header Bar -->
			<div class="bg-base-100 border-b border-base-300 px-4 py-2 flex-shrink-0">
				<div class="flex items-center justify-between">
					<div class="flex items-center space-x-4">
						<div class="flex items-center space-x-2">
							<ShareIcon class="w-6 h-6" />
							<h1 class="text-lg font-semibold">{{ shareInfo.title }}</h1>
						</div>
						<div class="text-sm text-base-content/60">
							{{ t("shares.by") }} {{ shareInfo.owner_name }}
						</div>
					</div>
					<div class="flex items-center space-x-2">
						<!-- Run Button -->
						<button
							v-if="canRun && currentView === 'workflow'"
							class="btn btn-sm btn-primary"
							@click="handleRun"
						>
							<PlayIcon class="w-4 h-4 mr-1" />
							{{ t("shares.run") }}
						</button>
						<!-- Back to Workflow -->
						<button
							v-if="currentView === 'runtime'"
							class="btn btn-sm btn-ghost"
							@click="
								currentView = 'workflow';
								showRuntime = false;
							"
						>
							<ArrowLeftIcon class="w-4 h-4 mr-1" />
							{{ t("shares.back_to_workflow") }}
						</button>
						<!-- Copy Workflow -->
						<button
							v-if="canCopy"
							class="btn btn-sm btn-secondary"
							@click="handleCopyWorkflow"
						>
							<DocumentDuplicateIcon class="w-4 h-4 mr-1" />
							{{ t("shares.copy") }}
						</button>
						<!-- Login -->
						<a href="/login" class="btn btn-primary btn-sm">
							{{ t("shares.login") }}
						</a>
					</div>
				</div>
			</div>

			<!-- Welcome Message -->
			<div
				v-if="
					shareInfo.ui_config.welcome_message &&
					shareInfo.ui_config.welcome_message.trim()
				"
				class="bg-base-50 border-b border-base-300 px-4 py-3"
			>
				<div class="max-w-4xl mx-auto">
					<div class="alert alert-info">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							class="stroke-current shrink-0 w-6 h-6"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
							></path>
						</svg>
						<div class="whitespace-pre-wrap">
							{{ shareInfo.ui_config.welcome_message }}
						</div>
					</div>
				</div>
			</div>

			<!-- Workflow View -->
			<div v-if="currentView === 'workflow'" class="h-screen">
				<component_wait>
					<Workflow
						ref="workflowRef"
						:flowId="flowId"
						class="bg-base-100 w-full h-full"
						:data="parsedWorkflowData"
						:key="shareUuid"
						:options="nodeOptions"
						@node-config="onNodeConfig"
					/>
				</component_wait>
			</div>

			<!-- Runtime View -->
			<div v-if="currentView === 'runtime' && showRuntime" class="min-h-screen">
				<div
					class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen p-2 sm:p-6"
				>
					<div
						class="relative bg-white rounded-xl shadow-md box-border h-full p-2 sm:p-5"
					>
						<h1 class="text-xl font-bold rounded-md inline-block mb-4">
							# {{ workflowInfo.name }}
						</h1>
						<component_wait>
							<RuntimeEngine
								:project_id="shareUuid"
								:data="runtimeData"
								mode="run"
								:key="shareUuid"
								view_mode="default"
							/>
						</component_wait>
					</div>
				</div>
			</div>

			<!-- Config 组件 - 只读模式 -->
			<Config
				v-if="showConfig"
				@close="closeConfig"
				@delete="deleteNode"
				:id="configNodeID"
				:type="configNodeType"
				:flowId="configWorkflow"
				:readonly="true"
			/>
		</div>
	</div>
</template>
