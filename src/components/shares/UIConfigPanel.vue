<script setup lang="ts">
	import { computed } from "vue";
	import { useI18n } from "vue-i18n";
	import type { ShareUIConfig } from "@/api/shares";
	import { PlayIcon, DocumentDuplicateIcon } from "@heroicons/vue/24/outline";

	const { t } = useI18n();

	const props = defineProps<{
		modelValue: ShareUIConfig;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: ShareUIConfig): void;
	}>();

	const uiConfig = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const updateField = (field: keyof ShareUIConfig, value: any) => {
		uiConfig.value = {
			...uiConfig.value,
			[field]: value,
			// 确保 allow_view 和 show_workflow_graph 始终为 true
			allow_view: true,
			show_workflow_graph: true,
		};
	};

	const hasAnyPermission = computed(() => {
		return uiConfig.value.allow_run || uiConfig.value.allow_copy;
	});
</script>

<template>
	<div class="space-y-4">
		<!-- Permissions -->
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			<!-- Allow Run -->
			<div class="form-control">
				<label class="label cursor-pointer">
					<span class="label-text flex items-center gap-2">
						<PlayIcon class="w-4 h-4" />
						{{ t("shares.allow_run") }}
					</span>
					<input
						type="checkbox"
						class="toggle toggle-primary"
						:checked="uiConfig.allow_run"
						@change="
							updateField(
								'allow_run',
								($event.target as HTMLInputElement).checked
							)
						"
					/>
				</label>
				<label class="label">
					<span class="label-text-alt">{{ t("shares.allow_run_desc") }}</span>
				</label>
			</div>

			<!-- Allow Copy -->
			<div class="form-control">
				<label class="label cursor-pointer">
					<span class="label-text flex items-center gap-2">
						<DocumentDuplicateIcon class="w-4 h-4" />
						{{ t("shares.allow_copy") }}
					</span>
					<input
						type="checkbox"
						class="toggle toggle-primary"
						:checked="uiConfig.allow_copy"
						@change="
							updateField(
								'allow_copy',
								($event.target as HTMLInputElement).checked
							)
						"
					/>
				</label>
				<label class="label">
					<span class="label-text-alt">{{ t("shares.allow_copy_desc") }}</span>
				</label>
			</div>
		</div>

		<!-- Permission Validation Alert -->
		<div v-if="!hasAnyPermission" class="alert alert-error">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				class="stroke-current shrink-0 w-6 h-6"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<span>{{ t("shares.at_least_one_permission") }}</span>
		</div>

		<!-- Welcome Message -->
		<div class="form-control">
			<label class="label">
				<span class="label-text">{{ t("shares.welcome_message") }}</span>
			</label>
			<textarea
				:value="uiConfig.welcome_message"
				@input="
					updateField(
						'welcome_message',
						($event.target as HTMLTextAreaElement).value
					)
				"
				class="textarea textarea-bordered w-full"
				:placeholder="t('shares.welcome_message_placeholder')"
				rows="3"
			></textarea>
			<label class="label">
				<span class="label-text-alt">{{ t("shares.welcome_message_desc") }}</span>
			</label>
		</div>
	</div>
</template>
